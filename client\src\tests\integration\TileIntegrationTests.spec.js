import { test, expect } from '@playwright/test';

/**
 * Comprehensive Tile Integration Tests
 * 
 * Production-ready test suite covering:
 * - Cross-tile navigation and state preservation
 * - Data synchronization between tiles
 * - UI consistency and accessibility
 * - Performance and load testing
 * - User workflow validation
 */

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const BASE_URL = 'https://royalty.technology';

const TILES = [
  { name: 'Dashboard', path: `${BASE_URL}/`, selector: '[data-testid="dashboard"]' },
  { name: 'Track', path: `${BASE_URL}/track`, selector: '[data-testid="track-canvas"]' },
  { name: 'Earn', path: `${BASE_URL}/earn`, selector: '[data-testid="earn-canvas"]' },
  { name: 'Projects', path: `${BASE_URL}/projects`, selector: '[data-testid="projects-page"]' },
  { name: 'Teams', path: `${BASE_URL}/teams`, selector: '[data-testid="teams-page"]' },
  { name: 'Admin', path: `${BASE_URL}/admin`, selector: '[data-testid="admin-dashboard"]' }
];

test.describe('Tile Integration Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('https://royalty.technology/login');
    
    // Login with test credentials
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-button"]');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
  });

  test('should navigate between all tiles successfully', async ({ page }) => {
    for (const tile of TILES) {
      console.log(`Testing navigation to ${tile.name}`);
      
      // Navigate to tile
      await page.goto(tile.path);
      
      // Wait for tile to load
      await page.waitForSelector(tile.selector, { timeout: 10000 });
      
      // Verify tile is loaded
      const tileElement = await page.locator(tile.selector);
      await expect(tileElement).toBeVisible();
      
      // Check for loading states
      const loadingElements = await page.locator('[data-testid*="loading"]').count();
      expect(loadingElements).toBe(0);
    }
  });

  test('should preserve navigation context between tiles', async ({ page }) => {
    // Start on Track tile
    await page.goto(`${BASE_URL}/track`);
    await page.waitForSelector('[data-testid="track-canvas"]');
    
    // Look for cross-tile navigation bridge
    const navigationBridge = page.locator('[data-testid="cross-tile-navigation"]');
    if (await navigationBridge.count() > 0) {
      // Click on a navigation suggestion
      const earnSuggestion = navigationBridge.locator('text=Earn Revenue').first();
      if (await earnSuggestion.count() > 0) {
        await earnSuggestion.click();
        
        // Verify navigation to Earn tile
        await page.waitForSelector('[data-testid="earn-canvas"]');
        await expect(page).toHaveURL(/\/earn/);
      }
    }
  });

  test('should maintain consistent UI patterns across tiles', async ({ page }) => {
    for (const tile of TILES) {
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Check for consistent header structure
      const header = page.locator('h1').first();
      await expect(header).toBeVisible();
      
      // Check for consistent card layouts
      const cards = page.locator('[class*="bg-white/5"]');
      if (await cards.count() > 0) {
        const firstCard = cards.first();
        await expect(firstCard).toBeVisible();
      }
      
      // Check for consistent button styles
      const buttons = page.locator('button');
      if (await buttons.count() > 0) {
        const firstButton = buttons.first();
        await expect(firstButton).toBeVisible();
      }
    }
  });

  test('should handle data synchronization between Track and Earn tiles', async ({ page }) => {
    // Navigate to Track tile
    await page.goto('/track');
    await page.waitForSelector('[data-testid="track-canvas"]');
    
    // Look for contribution form
    const contributionForm = page.locator('[data-testid="contribution-form"]');
    if (await contributionForm.count() > 0) {
      // Fill out a test contribution
      await page.fill('[data-testid="task-description"]', 'Test contribution for sync');
      await page.fill('[data-testid="hours-input"]', '2');
      await page.selectOption('[data-testid="difficulty-select"]', '3');
      
      // Submit contribution
      await page.click('[data-testid="submit-contribution"]');
      
      // Wait for success message
      await page.waitForSelector('[data-testid="success-message"]', { timeout: 5000 });
    }
    
    // Navigate to Earn tile
    await page.goto('/earn');
    await page.waitForSelector('[data-testid="earn-canvas"]');
    
    // Check if the contribution appears in earnings
    const contributionsList = page.locator('[data-testid="contributions-list"]');
    if (await contributionsList.count() > 0) {
      const testContribution = contributionsList.locator('text=Test contribution for sync');
      // Note: This might not be immediately visible due to approval workflows
      // but we can check that the list exists and is populated
      await expect(contributionsList).toBeVisible();
    }
  });

  test('should be accessible with keyboard navigation', async ({ page }) => {
    await page.goto('/');
    
    // Test tab navigation
    await page.keyboard.press('Tab');
    
    // Check that focus is visible
    const focusedElement = await page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Test navigation with Enter key
    await page.keyboard.press('Enter');
    
    // Verify page didn't crash
    await page.waitForTimeout(1000);
    const errorElements = await page.locator('[data-testid="error"]').count();
    expect(errorElements).toBe(0);
  });

  test('should handle responsive design across different screen sizes', async ({ page }) => {
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 1024, height: 768, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const viewport of viewports) {
      console.log(`Testing ${viewport.name} viewport`);
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      // Test main tiles
      for (const tile of TILES.slice(0, 3)) { // Test first 3 tiles for performance
        await page.goto(tile.path);
        await page.waitForSelector(tile.selector);
        
        // Check that content is visible and not overflowing
        const tileElement = page.locator(tile.selector);
        await expect(tileElement).toBeVisible();
        
        // Check for horizontal scroll (should not exist)
        const bodyScrollWidth = await page.evaluate(() => document.body.scrollWidth);
        const bodyClientWidth = await page.evaluate(() => document.body.clientWidth);
        expect(bodyScrollWidth).toBeLessThanOrEqual(bodyClientWidth + 20); // Allow small tolerance
      }
    }
  });

  test('should load tiles within performance thresholds', async ({ page }) => {
    for (const tile of TILES) {
      const startTime = Date.now();
      
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      const loadTime = Date.now() - startTime;
      
      // Tiles should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
      
      console.log(`${tile.name} tile loaded in ${loadTime}ms`);
    }
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Test with invalid routes
    await page.goto('/invalid-route');
    
    // Should show 404 page or redirect
    await page.waitForTimeout(2000);
    const currentUrl = page.url();
    
    // Either shows 404 content or redirects to valid page
    const notFoundElement = await page.locator('text=404').count();
    const validRedirect = TILES.some(tile => currentUrl.includes(tile.path));
    
    expect(notFoundElement > 0 || validRedirect).toBeTruthy();
  });

  test('should maintain authentication state across tiles', async ({ page }) => {
    // Verify user is authenticated on each tile
    for (const tile of TILES) {
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Should not redirect to login
      expect(page.url()).not.toContain('/login');
      
      // Should show user-specific content
      const userElements = await page.locator('[data-testid*="user"]').count();
      expect(userElements).toBeGreaterThan(0);
    }
  });

  test('should handle concurrent tile operations', async ({ page, context }) => {
    // Open multiple tabs
    const page2 = await context.newPage();
    
    // Navigate to different tiles simultaneously
    await Promise.all([
      page.goto('/track'),
      page2.goto('/earn')
    ]);
    
    // Wait for both to load
    await Promise.all([
      page.waitForSelector('[data-testid="track-canvas"]'),
      page2.waitForSelector('[data-testid="earn-canvas"]')
    ]);
    
    // Verify both tiles are functional
    await expect(page.locator('[data-testid="track-canvas"]')).toBeVisible();
    await expect(page2.locator('[data-testid="earn-canvas"]')).toBeVisible();
    
    await page2.close();
  });
});

test.describe('Admin Tile Specific Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('https://royalty.technology/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="dashboard"]');
  });

  test('should access admin dashboard without 404 errors', async ({ page }) => {
    await page.goto('/admin');
    
    // Should not show 404 error
    const notFoundElements = await page.locator('text=404').count();
    expect(notFoundElements).toBe(0);
    
    // Should show admin content or access control message
    await page.waitForTimeout(2000);
    const adminContent = await page.locator('[data-testid="admin-dashboard"]').count();
    const accessMessage = await page.locator('text=access').count();
    
    expect(adminContent > 0 || accessMessage > 0).toBeTruthy();
  });

  test('should navigate between admin tabs', async ({ page }) => {
    await page.goto('/admin');
    await page.waitForTimeout(2000);
    
    // Look for admin tabs
    const tabs = page.locator('[role="tab"]');
    const tabCount = await tabs.count();
    
    if (tabCount > 0) {
      // Click through available tabs
      for (let i = 0; i < Math.min(tabCount, 3); i++) {
        await tabs.nth(i).click();
        await page.waitForTimeout(500);
        
        // Verify tab content loads
        const tabContent = page.locator('[role="tabpanel"]');
        await expect(tabContent).toBeVisible();
      }
    }
  });
});
