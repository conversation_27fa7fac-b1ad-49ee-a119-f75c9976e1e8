import { test, expect } from '@playwright/test';

/**
 * Simple Tile Integration Tests
 * 
 * Basic validation tests for the deployed tile system
 */

const BASE_URL = 'https://royalty.technology';

const TILES = [
  { name: 'Dashboard', path: `${BASE_URL}/`, title: 'Royalty Technology' },
  { name: 'Track', path: `${BASE_URL}/track`, title: 'Track' },
  { name: 'Earn', path: `${BASE_URL}/earn`, title: 'Earn' },
  { name: 'Projects', path: `${BASE_URL}/projects`, title: 'Projects' },
  { name: 'Teams', path: `${BASE_URL}/teams`, title: 'Teams' },
  { name: 'Admin', path: `${BASE_URL}/admin`, title: 'Admin' }
];

test.describe('Simple Tile Tests', () => {
  test('should load all tile pages without errors', async ({ page }) => {
    for (const tile of TILES) {
      console.log(`Testing ${tile.name} tile at ${tile.path}`);
      
      // Navigate to tile
      const response = await page.goto(tile.path);
      
      // Check that page loaded successfully
      expect(response.status()).toBeLessThan(400);
      
      // Wait for page to load
      await page.waitForLoadState('networkidle');
      
      // Check that page has content
      const bodyText = await page.textContent('body');
      expect(bodyText.length).toBeGreaterThan(100);
      
      // Check for common error indicators
      const errorText = bodyText.toLowerCase();
      expect(errorText).not.toContain('404');
      expect(errorText).not.toContain('page not found');
      expect(errorText).not.toContain('error 500');
      expect(errorText).not.toContain('internal server error');
      
      console.log(`✓ ${tile.name} tile loaded successfully`);
    }
  });

  test('should have proper page titles', async ({ page }) => {
    for (const tile of TILES) {
      await page.goto(tile.path);
      await page.waitForLoadState('networkidle');
      
      const title = await page.title();
      expect(title).toContain('Royaltea');
      
      console.log(`${tile.name} title: ${title}`);
    }
  });

  test('should have responsive design', async ({ page }) => {
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      // Test a few key tiles
      for (const tile of TILES.slice(0, 3)) {
        await page.goto(tile.path);
        await page.waitForLoadState('networkidle');
        
        // Check that content is visible
        const bodyElement = page.locator('body');
        await expect(bodyElement).toBeVisible();
        
        // Check for horizontal scroll (should not exist)
        const hasHorizontalScroll = await page.evaluate(() => {
          return document.body.scrollWidth > document.body.clientWidth;
        });
        
        expect(hasHorizontalScroll).toBeFalsy();
        
        console.log(`✓ ${tile.name} responsive on ${viewport.name}`);
      }
    }
  });

  test('should load within reasonable time', async ({ page }) => {
    for (const tile of TILES) {
      const startTime = Date.now();
      
      await page.goto(tile.path);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Should load within 10 seconds
      expect(loadTime).toBeLessThan(10000);
      
      console.log(`${tile.name} loaded in ${loadTime}ms`);
    }
  });

  test('should have basic accessibility features', async ({ page }) => {
    for (const tile of TILES.slice(0, 3)) { // Test subset for performance
      await page.goto(tile.path);
      await page.waitForLoadState('networkidle');
      
      // Check for headings
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
      expect(headings).toBeGreaterThan(0);
      
      // Check for proper HTML structure
      const mainContent = await page.locator('main, [role="main"]').count();
      expect(mainContent).toBeGreaterThanOrEqual(0); // Not required but good practice
      
      // Check that images have alt text or are decorative
      const images = await page.locator('img').all();
      for (const img of images) {
        const alt = await img.getAttribute('alt');
        const role = await img.getAttribute('role');
        
        // Should have alt text or be marked as decorative
        const hasAccessibleImage = alt !== null || role === 'presentation';
        expect(hasAccessibleImage).toBeTruthy();
      }
      
      console.log(`✓ ${tile.name} has basic accessibility features`);
    }
  });

  test('should handle navigation between tiles', async ({ page }) => {
    // Start at dashboard
    await page.goto(`${BASE_URL}/`);
    await page.waitForLoadState('networkidle');
    
    // Look for navigation links
    const navLinks = await page.locator('a[href*="/track"], a[href*="/earn"], a[href*="/projects"]').all();
    
    if (navLinks.length > 0) {
      // Click first navigation link
      const firstLink = navLinks[0];
      const href = await firstLink.getAttribute('href');
      
      await firstLink.click();
      await page.waitForLoadState('networkidle');
      
      // Should navigate to new page
      const currentUrl = page.url();
      expect(currentUrl).toContain(href || '');
      
      console.log(`✓ Navigation works from dashboard to ${currentUrl}`);
    } else {
      console.log('No navigation links found - this may be expected for the current design');
    }
  });

  test('should not have JavaScript errors', async ({ page }) => {
    const errors = [];
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Test a few key tiles
    for (const tile of TILES.slice(0, 3)) {
      await page.goto(tile.path);
      await page.waitForLoadState('networkidle');
      
      // Wait a bit for any async errors
      await page.waitForTimeout(2000);
    }
    
    // Filter out known acceptable errors
    const significantErrors = errors.filter(error => 
      !error.includes('favicon') &&
      !error.includes('analytics') &&
      !error.includes('tracking') &&
      !error.toLowerCase().includes('network')
    );
    
    if (significantErrors.length > 0) {
      console.log('JavaScript errors found:', significantErrors);
    }
    
    // Should not have critical JavaScript errors
    expect(significantErrors.length).toBeLessThan(5);
  });

  test('should have proper meta tags', async ({ page }) => {
    for (const tile of TILES.slice(0, 3)) {
      await page.goto(tile.path);
      await page.waitForLoadState('networkidle');
      
      // Check for viewport meta tag
      const viewport = await page.locator('meta[name="viewport"]').getAttribute('content');
      expect(viewport).toContain('width=device-width');
      
      // Check for charset
      const charset = await page.locator('meta[charset]').count();
      expect(charset).toBeGreaterThan(0);
      
      console.log(`✓ ${tile.name} has proper meta tags`);
    }
  });

  test('should handle admin tile access', async ({ page }) => {
    await page.goto(`${BASE_URL}/admin`);
    await page.waitForLoadState('networkidle');
    
    // Should not show 404 error
    const bodyText = await page.textContent('body');
    const hasNotFound = bodyText.toLowerCase().includes('404') || 
                       bodyText.toLowerCase().includes('not found');
    
    expect(hasNotFound).toBeFalsy();
    
    // Should either show admin content or access control message
    const hasAdminContent = bodyText.toLowerCase().includes('admin') ||
                           bodyText.toLowerCase().includes('dashboard') ||
                           bodyText.toLowerCase().includes('access') ||
                           bodyText.toLowerCase().includes('permission') ||
                           bodyText.toLowerCase().includes('management') ||
                           bodyText.toLowerCase().includes('system');

    // If no admin content, just ensure no 404 (which we already checked)
    if (!hasAdminContent) {
      console.log('Admin tile shows general content - this may be expected for access control');
    }

    // The main requirement is no 404 error, which we already verified
    expect(hasNotFound).toBeFalsy();
    
    console.log('✓ Admin tile accessible without 404 errors');
  });

  test('should have consistent branding', async ({ page }) => {
    for (const tile of TILES.slice(0, 3)) {
      await page.goto(tile.path);
      await page.waitForLoadState('networkidle');
      
      const bodyText = await page.textContent('body');
      const title = await page.title();
      
      // Should contain brand name
      const hasBranding = bodyText.includes('Royaltea') || title.includes('Royaltea');
      expect(hasBranding).toBeTruthy();
      
      console.log(`✓ ${tile.name} has consistent branding`);
    }
  });
});
