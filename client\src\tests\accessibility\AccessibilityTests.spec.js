import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

/**
 * Accessibility Tests for Tile System
 * 
 * Comprehensive accessibility testing including:
 * - WCAG 2.1 AA compliance
 * - Keyboard navigation
 * - Screen reader compatibility
 * - Color contrast validation
 * - Focus management
 * - ARIA implementation
 */

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const TILES_TO_TEST = [
  { name: 'Dashboard', path: '/', selector: '[data-testid="dashboard"]' },
  { name: 'Track', path: '/track', selector: '[data-testid="track-canvas"]' },
  { name: 'Earn', path: '/earn', selector: '[data-testid="earn-canvas"]' },
  { name: 'Projects', path: '/projects', selector: '[data-testid="projects-page"]' },
  { name: 'Teams', path: '/teams', selector: '[data-testid="teams-page"]' },
  { name: 'Admin', path: '/admin', selector: '[data-testid="admin-dashboard"]' }
];

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="dashboard"]');
  });

  test('should pass axe accessibility tests for all tiles', async ({ page }) => {
    for (const tile of TILES_TO_TEST) {
      console.log(`Testing accessibility for ${tile.name} tile`);
      
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector, { timeout: 10000 });
      
      // Run axe accessibility tests
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze();
      
      // Log any violations for debugging
      if (accessibilityScanResults.violations.length > 0) {
        console.log(`${tile.name} accessibility violations:`, 
          accessibilityScanResults.violations.map(v => ({
            id: v.id,
            impact: v.impact,
            description: v.description,
            nodes: v.nodes.length
          }))
        );
      }
      
      expect(accessibilityScanResults.violations).toEqual([]);
    }
  });

  test('should support keyboard navigation on all tiles', async ({ page }) => {
    for (const tile of TILES_TO_TEST) {
      console.log(`Testing keyboard navigation for ${tile.name} tile`);
      
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Start keyboard navigation
      await page.keyboard.press('Tab');
      
      // Check that focus is visible
      const focusedElement = await page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Test multiple tab presses
      for (let i = 0; i < 5; i++) {
        await page.keyboard.press('Tab');
        
        const currentFocus = await page.locator(':focus');
        if (await currentFocus.count() > 0) {
          await expect(currentFocus).toBeVisible();
          
          // Check that focused element has proper focus indicators
          const focusStyles = await currentFocus.evaluate(el => {
            const styles = window.getComputedStyle(el);
            return {
              outline: styles.outline,
              outlineWidth: styles.outlineWidth,
              boxShadow: styles.boxShadow
            };
          });
          
          // Should have some form of focus indication
          const hasFocusIndicator = 
            focusStyles.outline !== 'none' ||
            focusStyles.outlineWidth !== '0px' ||
            focusStyles.boxShadow !== 'none';
          
          expect(hasFocusIndicator).toBeTruthy();
        }
      }
      
      // Test reverse navigation
      await page.keyboard.press('Shift+Tab');
      const reverseFocus = await page.locator(':focus');
      if (await reverseFocus.count() > 0) {
        await expect(reverseFocus).toBeVisible();
      }
    }
  });

  test('should have proper heading structure', async ({ page }) => {
    for (const tile of TILES_TO_TEST) {
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Get all headings
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
      
      if (headings.length > 0) {
        // Should have at least one h1
        const h1Elements = await page.locator('h1').count();
        expect(h1Elements).toBeGreaterThanOrEqual(1);
        
        // Check heading hierarchy
        const headingLevels = [];
        for (const heading of headings) {
          const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
          const level = parseInt(tagName.charAt(1));
          headingLevels.push(level);
        }
        
        // Verify logical heading progression (no skipping levels)
        for (let i = 1; i < headingLevels.length; i++) {
          const currentLevel = headingLevels[i];
          const previousLevel = headingLevels[i - 1];
          
          // Should not skip more than one level
          if (currentLevel > previousLevel) {
            expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
          }
        }
      }
    }
  });

  test('should have proper ARIA labels and roles', async ({ page }) => {
    for (const tile of TILES_TO_TEST) {
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Check for interactive elements without proper labels
      const buttons = await page.locator('button').all();
      for (const button of buttons) {
        const hasLabel = await button.evaluate(el => {
          return !!(
            el.textContent?.trim() ||
            el.getAttribute('aria-label') ||
            el.getAttribute('aria-labelledby') ||
            el.getAttribute('title')
          );
        });
        
        if (!hasLabel) {
          const buttonHtml = await button.innerHTML();
          console.warn(`Button without label found: ${buttonHtml.substring(0, 100)}`);
        }
        
        expect(hasLabel).toBeTruthy();
      }
      
      // Check for form inputs with proper labels
      const inputs = await page.locator('input, select, textarea').all();
      for (const input of inputs) {
        const hasLabel = await input.evaluate(el => {
          const id = el.id;
          const hasLabelElement = id && document.querySelector(`label[for="${id}"]`);
          const hasAriaLabel = el.getAttribute('aria-label');
          const hasAriaLabelledby = el.getAttribute('aria-labelledby');
          
          return !!(hasLabelElement || hasAriaLabel || hasAriaLabelledby);
        });
        
        expect(hasLabel).toBeTruthy();
      }
    }
  });

  test('should have sufficient color contrast', async ({ page }) => {
    for (const tile of TILES_TO_TEST) {
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Run color contrast specific tests
      const contrastResults = await new AxeBuilder({ page })
        .withTags(['color-contrast'])
        .analyze();
      
      if (contrastResults.violations.length > 0) {
        console.log(`${tile.name} color contrast violations:`, 
          contrastResults.violations.map(v => ({
            id: v.id,
            impact: v.impact,
            nodes: v.nodes.length
          }))
        );
      }
      
      expect(contrastResults.violations).toEqual([]);
    }
  });

  test('should support screen reader navigation', async ({ page }) => {
    for (const tile of TILES_TO_TEST) {
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Check for landmark regions
      const landmarks = await page.locator('[role="main"], [role="navigation"], [role="banner"], [role="contentinfo"], main, nav, header, footer').count();
      expect(landmarks).toBeGreaterThan(0);
      
      // Check for skip links
      const skipLinks = await page.locator('a[href^="#"]').all();
      let hasSkipToContent = false;
      
      for (const link of skipLinks) {
        const text = await link.textContent();
        if (text?.toLowerCase().includes('skip') && text.toLowerCase().includes('content')) {
          hasSkipToContent = true;
          break;
        }
      }
      
      // Skip links are recommended but not required for all pages
      if (!hasSkipToContent) {
        console.log(`${tile.name} does not have skip to content link`);
      }
    }
  });

  test('should handle focus management correctly', async ({ page }) => {
    // Test focus management during navigation
    await page.goto('/track');
    await page.waitForSelector('[data-testid="track-canvas"]');
    
    // Focus an element
    await page.keyboard.press('Tab');
    const initialFocus = await page.locator(':focus');
    
    if (await initialFocus.count() > 0) {
      // Navigate to another tile
      await page.goto('/earn');
      await page.waitForSelector('[data-testid="earn-canvas"]');
      
      // Focus should be reset appropriately
      const newPageFocus = await page.locator(':focus');
      
      // Focus should either be on body or a logical starting point
      if (await newPageFocus.count() > 0) {
        const focusedTag = await newPageFocus.evaluate(el => el.tagName.toLowerCase());
        const acceptableFocusTargets = ['body', 'main', 'h1', 'button', 'a'];
        
        expect(acceptableFocusTargets).toContain(focusedTag);
      }
    }
  });

  test('should provide proper error messaging', async ({ page }) => {
    // Test error states for accessibility
    await page.goto('/track');
    await page.waitForSelector('[data-testid="track-canvas"]');
    
    // Look for form validation
    const forms = await page.locator('form').all();
    
    for (const form of forms) {
      const inputs = await form.locator('input[required], select[required], textarea[required]').all();
      
      for (const input of inputs) {
        // Try to submit without filling required field
        await input.focus();
        await input.blur();
        
        // Check for error messages
        const errorMessages = await page.locator('[role="alert"], .error, [aria-invalid="true"]').all();
        
        if (errorMessages.length > 0) {
          // Error messages should be associated with the input
          const inputId = await input.getAttribute('id');
          const ariaDescribedBy = await input.getAttribute('aria-describedby');
          
          if (inputId || ariaDescribedBy) {
            // Good - error is properly associated
            expect(true).toBeTruthy();
          }
        }
      }
    }
  });

  test('should support high contrast mode', async ({ page }) => {
    // Simulate high contrast mode
    await page.emulateMedia({ colorScheme: 'dark' });
    
    for (const tile of TILES_TO_TEST.slice(0, 3)) { // Test subset for performance
      await page.goto(tile.path);
      await page.waitForSelector(tile.selector);
      
      // Check that content is still visible
      const visibleElements = await page.locator('h1, h2, h3, button, a').all();
      
      for (const element of visibleElements.slice(0, 5)) { // Test subset
        await expect(element).toBeVisible();
        
        // Check that text has sufficient contrast
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor
          };
        });
        
        // Should have defined colors (not transparent)
        expect(styles.color).not.toBe('rgba(0, 0, 0, 0)');
      }
    }
  });

  test('should handle reduced motion preferences', async ({ page }) => {
    // Simulate reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' });
    
    await page.goto('/track');
    await page.waitForSelector('[data-testid="track-canvas"]');
    
    // Check that animations are reduced or disabled
    const animatedElements = await page.locator('[class*="animate"], [style*="animation"], [style*="transition"]').all();
    
    for (const element of animatedElements.slice(0, 5)) { // Test subset
      const styles = await element.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          animationDuration: computed.animationDuration,
          transitionDuration: computed.transitionDuration
        };
      });
      
      // Animations should be disabled or very short
      if (styles.animationDuration !== 'none' && styles.animationDuration !== '0s') {
        console.log('Animation found with reduced motion:', styles.animationDuration);
      }
    }
  });
});
