import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import CrossTileNavigationBridge from '../../components/navigation/CrossTileNavigationBridge';
import { DataSyncProvider } from '../../contexts/DataSyncContext';
import { NavigationProvider } from '../../contexts/NavigationContext';

/**
 * Unit Tests for Cross-Tile Navigation Bridge
 * 
 * Tests the core functionality of seamless navigation between tiles
 * including context preservation, smart suggestions, and state management.
 */

// Mock the navigation context
const mockNavigationActions = {
  navigateToCanvas: vi.fn(),
  setActiveCanvas: vi.fn(),
  updateNavigationState: vi.fn()
};

const MockNavigationProvider = ({ children }) => (
  <NavigationProvider value={{ actions: mockNavigationActions }}>
    {children}
  </NavigationProvider>
);

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/track' })
  };
});

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <DataSyncProvider>
      <MockNavigationProvider>
        {children}
      </MockNavigationProvider>
    </DataSyncProvider>
  </BrowserRouter>
);

describe('CrossTileNavigationBridge', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders navigation suggestions for track tile', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Smart Navigation')).toBeInTheDocument();
    });

    // Should show suggestions for related tiles
    expect(screen.getByText(/Earn Revenue/i)).toBeInTheDocument();
    expect(screen.getByText(/Manage Projects/i)).toBeInTheDocument();
  });

  it('renders navigation suggestions for earn tile', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="earn" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Smart Navigation')).toBeInTheDocument();
    });

    // Should show suggestions for related tiles
    expect(screen.getByText(/Track Progress/i)).toBeInTheDocument();
  });

  it('handles navigation clicks correctly', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const earnButton = screen.getByText(/Earn Revenue/i);
      expect(earnButton).toBeInTheDocument();
    });

    const earnButton = screen.getByText(/Earn Revenue/i);
    fireEvent.click(earnButton);

    await waitFor(() => {
      expect(mockNavigationActions.navigateToCanvas).toHaveBeenCalledWith(
        'earn',
        expect.objectContaining({
          method: 'bridge-navigation'
        })
      );
      expect(mockNavigate).toHaveBeenCalledWith('/earn');
    });
  });

  it('shows workflow-based suggestions', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      // Should show workflow suggestions
      const workflowElements = screen.getAllByText(/workflow/i);
      expect(workflowElements.length).toBeGreaterThan(0);
    });
  });

  it('handles admin tile navigation', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="admin" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Smart Navigation')).toBeInTheDocument();
    });

    // Should show admin-specific suggestions
    expect(screen.getByText(/System Management/i)).toBeInTheDocument();
  });

  it('can be disabled via props', () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge 
          currentTile="track" 
          showSuggestions={false}
          showQuickActions={false}
        />
      </TestWrapper>
    );

    // Should not render anything when both props are false
    expect(screen.queryByText('Smart Navigation')).not.toBeInTheDocument();
    expect(screen.queryByText('Quick Actions')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <TestWrapper>
        <CrossTileNavigationBridge 
          currentTile="track" 
          className="custom-class"
        />
      </TestWrapper>
    );

    expect(container.querySelector('.custom-class')).toBeInTheDocument();
  });

  it('handles loading states during navigation', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const earnButton = screen.getByText(/Earn Revenue/i);
      expect(earnButton).toBeInTheDocument();
    });

    const earnButton = screen.getByText(/Earn Revenue/i);
    fireEvent.click(earnButton);

    // Should show loading state briefly
    await waitFor(() => {
      expect(earnButton).toBeDisabled();
    });
  });

  it('generates correct tile routes', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const projectsButton = screen.getByText(/Manage Projects/i);
      expect(projectsButton).toBeInTheDocument();
    });

    const projectsButton = screen.getByText(/Manage Projects/i);
    fireEvent.click(projectsButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/projects');
    });
  });

  it('preserves navigation context', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const earnButton = screen.getByText(/Earn Revenue/i);
      expect(earnButton).toBeInTheDocument();
    });

    const earnButton = screen.getByText(/Earn Revenue/i);
    fireEvent.click(earnButton);

    await waitFor(() => {
      expect(mockNavigationActions.navigateToCanvas).toHaveBeenCalledWith(
        'earn',
        expect.objectContaining({
          context: expect.any(Object),
          method: 'bridge-navigation'
        })
      );
    });
  });
});

describe('CrossTileNavigationBridge Accessibility', () => {
  it('has proper ARIA labels', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
    });

    // All buttons should be accessible
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toBeVisible();
      expect(button).not.toHaveAttribute('aria-hidden', 'true');
    });
  });

  it('supports keyboard navigation', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const firstButton = screen.getAllByRole('button')[0];
      expect(firstButton).toBeInTheDocument();
    });

    const firstButton = screen.getAllByRole('button')[0];
    
    // Should be focusable
    firstButton.focus();
    expect(document.activeElement).toBe(firstButton);

    // Should respond to Enter key
    fireEvent.keyDown(firstButton, { key: 'Enter', code: 'Enter' });
    
    // Navigation should be triggered
    await waitFor(() => {
      expect(mockNavigationActions.navigateToCanvas).toHaveBeenCalled();
    });
  });

  it('has proper heading structure', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const heading = screen.getByText('Smart Navigation');
      expect(heading).toBeInTheDocument();
    });

    const heading = screen.getByText('Smart Navigation');
    expect(heading.tagName).toBe('H3');
  });
});

describe('CrossTileNavigationBridge Performance', () => {
  it('renders efficiently with minimal re-renders', async () => {
    const renderSpy = vi.fn();
    
    const TestComponent = (props) => {
      renderSpy();
      return <CrossTileNavigationBridge {...props} />;
    };

    const { rerender } = render(
      <TestWrapper>
        <TestComponent currentTile="track" />
      </TestWrapper>
    );

    // Initial render
    expect(renderSpy).toHaveBeenCalledTimes(1);

    // Re-render with same props should not cause unnecessary renders
    rerender(
      <TestWrapper>
        <TestComponent currentTile="track" />
      </TestWrapper>
    );

    // Should have minimal additional renders
    expect(renderSpy).toHaveBeenCalledTimes(2);
  });

  it('handles rapid navigation clicks gracefully', async () => {
    render(
      <TestWrapper>
        <CrossTileNavigationBridge currentTile="track" />
      </TestWrapper>
    );

    await waitFor(() => {
      const earnButton = screen.getByText(/Earn Revenue/i);
      expect(earnButton).toBeInTheDocument();
    });

    const earnButton = screen.getByText(/Earn Revenue/i);
    
    // Rapid clicks
    fireEvent.click(earnButton);
    fireEvent.click(earnButton);
    fireEvent.click(earnButton);

    // Should handle gracefully without errors
    await waitFor(() => {
      expect(mockNavigationActions.navigateToCanvas).toHaveBeenCalled();
    });

    // Should not crash or cause infinite loops
    expect(screen.getByText(/Earn Revenue/i)).toBeInTheDocument();
  });
});
