import { test, expect } from '@playwright/test';

/**
 * Performance Tests for Tile System
 * 
 * Comprehensive performance testing including:
 * - Load time measurements
 * - Memory usage monitoring
 * - Network request optimization
 * - Bundle size analysis
 * - Runtime performance metrics
 */

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PERFORMANCE_THRESHOLDS = {
  pageLoad: 3000,        // 3 seconds max page load
  firstContentfulPaint: 1500,  // 1.5 seconds max FCP
  largestContentfulPaint: 2500, // 2.5 seconds max LCP
  cumulativeLayoutShift: 0.1,   // Max CLS score
  firstInputDelay: 100,         // 100ms max FID
  memoryUsage: 50 * 1024 * 1024, // 50MB max memory
  networkRequests: 20           // Max 20 network requests per page
};

test.describe('Tile Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Enable performance monitoring
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-button"]');
    await page.waitForSelector('[data-testid="dashboard"]');
  });

  test('Track tile should meet performance thresholds', async ({ page }) => {
    // Start performance monitoring
    const startTime = Date.now();
    
    // Navigate to Track tile
    await page.goto('/track');
    
    // Wait for tile to be fully loaded
    await page.waitForSelector('[data-testid="track-canvas"]');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Check load time
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad);
    console.log(`Track tile loaded in ${loadTime}ms`);
    
    // Get Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {};
        
        // Get FCP
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              vitals.fcp = entry.startTime;
            }
          }
        }).observe({ entryTypes: ['paint'] });
        
        // Get LCP
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.lcp = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Get CLS
        let clsValue = 0;
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          vitals.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
        
        setTimeout(() => resolve(vitals), 2000);
      });
    });
    
    // Validate Web Vitals
    if (vitals.fcp) {
      expect(vitals.fcp).toBeLessThan(PERFORMANCE_THRESHOLDS.firstContentfulPaint);
    }
    if (vitals.lcp) {
      expect(vitals.lcp).toBeLessThan(PERFORMANCE_THRESHOLDS.largestContentfulPaint);
    }
    if (vitals.cls !== undefined) {
      expect(vitals.cls).toBeLessThan(PERFORMANCE_THRESHOLDS.cumulativeLayoutShift);
    }
  });

  test('Earn tile should meet performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/earn');
    await page.waitForSelector('[data-testid="earn-canvas"]');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad);
    console.log(`Earn tile loaded in ${loadTime}ms`);
  });

  test('Admin tile should meet performance thresholds', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/admin');
    await page.waitForTimeout(2000); // Allow for access control checks
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.pageLoad);
    console.log(`Admin tile loaded in ${loadTime}ms`);
  });

  test('should optimize network requests', async ({ page }) => {
    // Monitor network requests
    const requests = [];
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType()
      });
    });
    
    await page.goto('/track');
    await page.waitForSelector('[data-testid="track-canvas"]');
    await page.waitForLoadState('networkidle');
    
    // Filter out non-essential requests
    const essentialRequests = requests.filter(req => 
      !req.url.includes('analytics') && 
      !req.url.includes('tracking') &&
      req.resourceType !== 'image' ||
      req.url.includes('favicon')
    );
    
    console.log(`Total requests: ${requests.length}, Essential: ${essentialRequests.length}`);
    
    // Should not exceed threshold for essential requests
    expect(essentialRequests.length).toBeLessThan(PERFORMANCE_THRESHOLDS.networkRequests);
  });

  test('should handle memory usage efficiently', async ({ page }) => {
    // Navigate through multiple tiles to test memory usage
    const tiles = ['/track', '/earn', '/projects', '/teams'];
    
    for (const tile of tiles) {
      await page.goto(tile);
      await page.waitForTimeout(1000);
      
      // Get memory usage
      const memoryUsage = await page.evaluate(() => {
        if (performance.memory) {
          return {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit
          };
        }
        return null;
      });
      
      if (memoryUsage) {
        console.log(`Memory usage on ${tile}: ${Math.round(memoryUsage.used / 1024 / 1024)}MB`);
        expect(memoryUsage.used).toBeLessThan(PERFORMANCE_THRESHOLDS.memoryUsage);
      }
    }
  });

  test('should handle rapid navigation without performance degradation', async ({ page }) => {
    const tiles = ['/track', '/earn', '/projects', '/teams', '/admin'];
    const navigationTimes = [];
    
    for (let i = 0; i < 3; i++) { // Test 3 rounds of rapid navigation
      for (const tile of tiles) {
        const startTime = Date.now();
        
        await page.goto(tile);
        await page.waitForTimeout(500); // Minimal wait
        
        const navigationTime = Date.now() - startTime;
        navigationTimes.push(navigationTime);
      }
    }
    
    // Calculate average navigation time
    const avgNavigationTime = navigationTimes.reduce((a, b) => a + b, 0) / navigationTimes.length;
    console.log(`Average navigation time: ${avgNavigationTime}ms`);
    
    // Should maintain reasonable performance even with rapid navigation
    expect(avgNavigationTime).toBeLessThan(2000);
    
    // No navigation should take excessively long
    const maxNavigationTime = Math.max(...navigationTimes);
    expect(maxNavigationTime).toBeLessThan(5000);
  });

  test('should optimize bundle loading', async ({ page }) => {
    // Monitor resource loading
    const resources = [];
    page.on('response', response => {
      if (response.url().includes('.js') || response.url().includes('.css')) {
        resources.push({
          url: response.url(),
          size: response.headers()['content-length'],
          status: response.status()
        });
      }
    });
    
    await page.goto('/track');
    await page.waitForLoadState('networkidle');
    
    // Check that resources loaded successfully
    const failedResources = resources.filter(r => r.status >= 400);
    expect(failedResources.length).toBe(0);
    
    console.log(`Loaded ${resources.length} JS/CSS resources`);
    
    // Log largest resources for optimization insights
    const largeResources = resources
      .filter(r => r.size && parseInt(r.size) > 100000)
      .sort((a, b) => parseInt(b.size) - parseInt(a.size));
    
    if (largeResources.length > 0) {
      console.log('Large resources:', largeResources.slice(0, 3));
    }
  });

  test('should maintain performance under load simulation', async ({ page, context }) => {
    // Simulate multiple concurrent users
    const pages = [];
    
    try {
      // Create multiple pages to simulate concurrent load
      for (let i = 0; i < 3; i++) {
        const newPage = await context.newPage();
        pages.push(newPage);
        
        // Login each page
        await newPage.goto('/login');
        await newPage.fill('[data-testid="email-input"]', TEST_USER.email);
        await newPage.fill('[data-testid="password-input"]', TEST_USER.password);
        await newPage.click('[data-testid="login-button"]');
        await newPage.waitForSelector('[data-testid="dashboard"]');
      }
      
      // Navigate all pages to different tiles simultaneously
      const navigationPromises = pages.map((p, index) => {
        const tiles = ['/track', '/earn', '/projects'];
        return p.goto(tiles[index % tiles.length]);
      });
      
      const startTime = Date.now();
      await Promise.all(navigationPromises);
      
      // Wait for all pages to load
      await Promise.all(pages.map(p => p.waitForLoadState('networkidle')));
      
      const totalTime = Date.now() - startTime;
      console.log(`Concurrent navigation completed in ${totalTime}ms`);
      
      // Should handle concurrent load reasonably
      expect(totalTime).toBeLessThan(10000);
      
    } finally {
      // Clean up
      await Promise.all(pages.map(p => p.close()));
    }
  });

  test('should optimize image and asset loading', async ({ page }) => {
    const imageRequests = [];
    
    page.on('request', request => {
      if (request.resourceType() === 'image') {
        imageRequests.push(request.url());
      }
    });
    
    await page.goto('/track');
    await page.waitForSelector('[data-testid="track-canvas"]');
    await page.waitForLoadState('networkidle');
    
    console.log(`Loaded ${imageRequests.length} images`);
    
    // Should not load excessive images
    expect(imageRequests.length).toBeLessThan(10);
    
    // Check for optimized image formats
    const unoptimizedImages = imageRequests.filter(url => 
      !url.includes('.webp') && 
      !url.includes('.avif') && 
      url.includes('.png') || url.includes('.jpg')
    );
    
    // Log unoptimized images for improvement
    if (unoptimizedImages.length > 0) {
      console.log('Unoptimized images found:', unoptimizedImages.length);
    }
  });
});
